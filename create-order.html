<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建订单 - GoMyHire</title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" href="assets/styles.css">
    <link rel="stylesheet" href="assets/notification.css">
    
    <style>
        /* 专用于创建订单页面的样式 */
        .create-order-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .order-form-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .section-title {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-field {
            margin-bottom: 15px;
        }
        
        .form-field label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-field input,
        .form-field select,
        .form-field textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-field input:focus,
        .form-field select:focus,
        .form-field textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .required {
            color: #e74c3c;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .submit-section {
            text-align: center;
            padding: 30px;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .auth-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .message {
            padding: 12px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .hidden {
            display: none !important;
        }
        
        .main-title {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .auth-description {
            margin: 15px 0;
            color: #666;
        }
        
        /* 日志面板样式 */
        .log-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 70vh;
            background: rgba(0, 0, 0, 0.9);
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border-radius: 8px;
            overflow: hidden;
            z-index: 10000;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            transition: all 0.3s ease;
        }
        
        .log-panel.minimized {
            height: 40px;
            overflow: hidden;
        }
        
        .log-header {
            background: #333;
            padding: 8px 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        .log-header h4 {
            margin: 0;
            color: #fff;
            font-size: 14px;
        }
        
        .log-controls {
            display: flex;
            gap: 8px;
        }
        
        .log-btn {
            background: #555;
            color: #fff;
            border: none;
            padding: 2px 6px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
        }
        
        .log-btn:hover {
            background: #777;
        }
        
        .log-content {
            padding: 10px;
            max-height: calc(70vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            border-left: 3px solid #00ff00;
        }
        
        .log-entry.request {
            border-left-color: #ff9500;
        }
        
        .log-entry.response {
            border-left-color: #0088ff;
        }
        
        .log-entry.error {
            border-left-color: #ff0000;
        }
        
        .log-timestamp {
            color: #888;
            font-size: 10px;
            margin-bottom: 4px;
        }
        
        .log-type {
            color: #fff;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .log-details {
            color: #00ff00;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .log-toggle {
            position: fixed;
            top: 20px;
            right: 440px;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 9999;
            font-size: 12px;
        }
        
        .log-toggle:hover {
            background: rgba(0, 0, 0, 0.9);
        }
    </style>
</head>
<body>
    <div class="create-order-container">
        <h1 class="main-title">创建订单系统</h1>
        
        <!-- 登录区域 -->
        <div id="authSection" class="auth-section">
            <h2>系统登录</h2>
            <p class="auth-description">请先登录以获取系统数据</p>
            
            <form id="loginForm" class="login-form">
                <div class="form-field">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                
                <div class="form-field">
                    <label for="password">密码</label>
                    <input type="password" id="password" value="Gomyhire@123456" required>
                </div>
                
                <button type="submit" class="submit-btn">登录系统</button>
            </form>
            
            <div id="authMessage"></div>
        </div>
        
        <!-- 创建订单表单 -->
        <div id="orderFormContainer" class="hidden">
            <form id="createOrderForm">
                <!-- 基本信息 -->
                <div class="order-form-section">
                    <h3 class="section-title">基本信息</h3>
                    <div class="form-row">
                        <div class="form-field">
                            <label for="subCategoryId">子类别 <span class="required">*</span></label>
                            <select id="subCategoryId" required>
                                <option value="">请选择子类别</option>
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label for="carTypeId">车型 <span class="required">*</span></label>
                            <select id="carTypeId" required>
                                <option value="">请选择车型</option>
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label for="inchargeByBackendUserId">负责用户 <span class="required">*</span></label>
                            <select id="inchargeByBackendUserId" required>
                                <option value="">请选择负责用户</option>
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label for="otaReferenceNumber">OTA参考号 <span class="required">*</span></label>
                            <input type="text" id="otaReferenceNumber" required>
                        </div>
                    </div>
                </div>
                
                <!-- 客户信息 -->
                <div class="order-form-section">
                    <h3 class="section-title">客户信息</h3>
                    <div class="form-row">
                        <div class="form-field">
                            <label for="customerName">客户姓名</label>
                            <input type="text" id="customerName">
                        </div>
                        
                        <div class="form-field">
                            <label for="customerContact">客户联系方式</label>
                            <input type="text" id="customerContact">
                        </div>
                        
                        <div class="form-field">
                            <label for="customerEmail">客户邮箱</label>
                            <input type="email" id="customerEmail">
                        </div>
                        
                        <div class="form-field">
                            <label for="passengerNumber">乘客人数</label>
                            <input type="number" id="passengerNumber" min="1">
                        </div>
                        
                        <div class="form-field">
                            <label for="luggageNumber">行李件数</label>
                            <input type="number" id="luggageNumber" min="0">
                        </div>
                    </div>
                </div>
                
                <!-- 行程信息 -->
                <div class="order-form-section">
                    <h3 class="section-title">行程信息</h3>
                    <div class="form-row">
                        <div class="form-field">
                            <label for="pickup">出发地点</label>
                            <input type="text" id="pickup">
                        </div>
                        
                        <div class="form-field">
                            <label for="destination">目的地</label>
                            <input type="text" id="destination">
                        </div>
                        
                        <div class="form-field">
                            <label for="date">日期</label>
                            <input type="date" id="date">
                        </div>
                        
                        <div class="form-field">
                            <label for="time">时间</label>
                            <input type="time" id="time">
                        </div>
                        
                        <div class="form-field">
                            <label for="pickupLat">出发地纬度</label>
                            <input type="number" id="pickupLat" step="any">
                        </div>
                        
                        <div class="form-field">
                            <label for="pickupLong">出发地经度</label>
                            <input type="number" id="pickupLong" step="any">
                        </div>
                        
                        <div class="form-field">
                            <label for="destinationLat">目的地纬度</label>
                            <input type="number" id="destinationLat" step="any">
                        </div>
                        
                        <div class="form-field">
                            <label for="destinationLong">目的地经度</label>
                            <input type="number" id="destinationLong" step="any">
                        </div>
                    </div>
                </div>
                
                <!-- 费用信息 -->
                <div class="order-form-section">
                    <h3 class="section-title">费用信息</h3>
                    <div class="form-row">
                        <div class="form-field">
                            <label for="otaPrice">OTA价格</label>
                            <input type="number" id="otaPrice" step="0.01">
                        </div>
                        
                        <div class="form-field">
                            <label for="driverFee">司机费用</label>
                            <input type="number" id="driverFee" step="0.01">
                        </div>
                        
                        <div class="form-field">
                            <label for="driverCollect">司机收取费用</label>
                            <input type="number" id="driverCollect" step="0.01">
                        </div>
                    </div>
                </div>
                
                <!-- 额外服务 -->
                <div class="order-form-section">
                    <h3 class="section-title">额外服务</h3>
                    <div class="form-row">
                        <div class="form-field">
                            <label for="tourGuide">导游服务</label>
                            <select id="tourGuide">
                                <option value="">请选择</option>
                                <option value="1">需要</option>
                                <option value="0">不需要</option>
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label for="babyChair">儿童座椅</label>
                            <select id="babyChair">
                                <option value="">请选择</option>
                                <option value="1">需要</option>
                                <option value="0">不需要</option>
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label for="meetAndGreet">接机服务</label>
                            <select id="meetAndGreet">
                                <option value="">请选择</option>
                                <option value="1">需要</option>
                                <option value="0">不需要</option>
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label for="drivingRegionId">驾驶区域</label>
                            <select id="drivingRegionId">
                                <option value="">请选择驾驶区域</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-field">
                        <label for="flightInfo">航班信息</label>
                        <textarea id="flightInfo" rows="3"></textarea>
                    </div>
                    
                    <div class="form-field">
                        <label for="extraRequirement">额外要求</label>
                        <textarea id="extraRequirement" rows="3"></textarea>
                    </div>
                    
                    <div class="form-field">
                        <label>语言要求</label>
                        <div id="languagesContainer" class="checkbox-group">
                            <!-- 语言选项将通过JavaScript动态添加 -->
                        </div>
                    </div>
                </div>
                
                <!-- 提交按钮 -->
                <div class="submit-section">
                    <button type="submit" class="submit-btn" id="submitBtn">创建订单</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <p id="loadingText">正在处理...</p>
        </div>
    </div>
    
    <!-- 通知容器 -->
    <div id="notification" class="notification hidden"></div>
    
    <!-- 日志控制按钮 -->
    <button id="logToggle" class="log-toggle">显示日志</button>
    
    <!-- 日志面板 -->
    <div id="logPanel" class="log-panel hidden">
        <div class="log-header" onclick="window.logger?.toggleMinimize()">
            <h4>🔍 API运行日志</h4>
            <div class="log-controls">
                <button class="log-btn" onclick="window.logger?.clear()">清空</button>
                <button class="log-btn" onclick="window.logger?.export()">导出</button>
                <button class="log-btn" onclick="window.logger?.hide()">隐藏</button>
            </div>
        </div>
        <div id="logContent" class="log-content">
            <!-- 日志条目将在这里显示 -->
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="components/notification.js"></script>
    <script src="create-order.js"></script>
</body>
</html>

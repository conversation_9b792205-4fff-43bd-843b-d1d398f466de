/**
 * @file create-order.js - 创建订单页面的主要逻辑
 * @description 处理登录、获取系统数据、创建订单等功能
 * <AUTHOR> IDE
 * @created_at 2025-01-14
 */

/**
 * @class GlobalLogger - 全局日志管理器
 * @description 用于记录和显示所有API请求和响应
 */
class GlobalLogger {
    constructor() {
        this.logs = [];
        this.isVisible = false;
        this.isMinimized = false;
        this.maxLogs = 100;
        
        this.initDOM();
        this.setupAxiosInterceptors();
        
        // 绑定到全局对象
        window.logger = this;
        
        console.log('GlobalLogger initialized');
    }
    
    /**
     * 初始化DOM元素
     */
    initDOM() {
        this.logPanel = document.getElementById('logPanel');
        this.logContent = document.getElementById('logContent');
        this.logToggle = document.getElementById('logToggle');
        
        // 绑定切换按钮事件
        if (this.logToggle) {
            this.logToggle.addEventListener('click', () => this.toggle());
        }
    }
    
    /**
     * 设置Axios拦截器
     */
    setupAxiosInterceptors() {
        // 请求拦截器
        axios.interceptors.request.use(
            (config) => {
                this.logRequest(config);
                return config;
            },
            (error) => {
                this.logError('请求发送失败', error);
                return Promise.reject(error);
            }
        );
        
        // 响应拦截器
        axios.interceptors.response.use(
            (response) => {
                this.logResponse(response);
                return response;
            },
            (error) => {
                this.logError('请求响应错误', error);
                return Promise.reject(error);
            }
        );
    }
    
    /**
     * 记录请求日志
     */
    logRequest(config) {
        const logEntry = {
            type: 'request',
            timestamp: new Date().toLocaleTimeString(),
            method: config.method.toUpperCase(),
            url: config.url,
            headers: config.headers,
            data: config.data,
            params: config.params
        };
        
        this.addLog(logEntry);
        console.log('🚀 API Request:', logEntry);
    }
    
    /**
     * 记录响应日志
     */
    logResponse(response) {
        const logEntry = {
            type: 'response',
            timestamp: new Date().toLocaleTimeString(),
            status: response.status,
            statusText: response.statusText,
            url: response.config.url,
            headers: response.headers,
            data: response.data
        };
        
        this.addLog(logEntry);
        console.log('✅ API Response:', logEntry);
    }
    
    /**
     * 记录错误日志
     */
    logError(message, error) {
        const logEntry = {
            type: 'error',
            timestamp: new Date().toLocaleTimeString(),
            message: message,
            error: error.message,
            url: error.config?.url,
            status: error.response?.status,
            data: error.response?.data
        };
        
        this.addLog(logEntry);
        console.error('❌ API Error:', logEntry);
    }
    
    /**
     * 添加日志条目
     */
    addLog(logEntry) {
        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        this.renderLog(logEntry);
    }
    
    /**
     * 渲染单个日志条目
     */
    renderLog(logEntry) {
        if (!this.logContent) return;
        
        const logElement = document.createElement('div');
        logElement.className = `log-entry ${logEntry.type}`;
        
        let content = '';
        let icon = '';
        
        switch (logEntry.type) {
            case 'request':
                icon = '🚀';
                content = `${logEntry.method} ${logEntry.url}`;
                if (logEntry.data) {
                    content += `\n📤 请求数据:\n${JSON.stringify(logEntry.data, null, 2)}`;
                }
                if (logEntry.params) {
                    content += `\n🔗 请求参数:\n${JSON.stringify(logEntry.params, null, 2)}`;
                }
                if (logEntry.headers?.Authorization) {
                    content += `\n🔑 认证: Bearer ${logEntry.headers.Authorization.substring(0, 20)}...`;
                }
                break;
                
            case 'response':
                icon = '✅';
                content = `${logEntry.status} ${logEntry.statusText} - ${logEntry.url}`;
                if (logEntry.data) {
                    content += `\n📥 响应数据:\n${JSON.stringify(logEntry.data, null, 2)}`;
                }
                break;
                
            case 'error':
                icon = '❌';
                content = `${logEntry.message} - ${logEntry.url || '未知URL'}`;
                if (logEntry.status) {
                    content += `\n状态码: ${logEntry.status}`;
                }
                if (logEntry.data) {
                    content += `\n错误详情:\n${JSON.stringify(logEntry.data, null, 2)}`;
                }
                break;
        }
        
        logElement.innerHTML = `
            <div class="log-timestamp">${logEntry.timestamp}</div>
            <div class="log-type">${icon} ${logEntry.type.toUpperCase()}</div>
            <div class="log-details">${content}</div>
        `;
        
        this.logContent.appendChild(logElement);
        
        // 自动滚动到底部
        this.logContent.scrollTop = this.logContent.scrollHeight;
    }
    
    /**
     * 切换日志面板显示/隐藏
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    /**
     * 显示日志面板
     */
    show() {
        if (this.logPanel) {
            this.logPanel.classList.remove('hidden');
            this.isVisible = true;
            if (this.logToggle) {
                this.logToggle.textContent = '隐藏日志';
            }
        }
    }
    
    /**
     * 隐藏日志面板
     */
    hide() {
        if (this.logPanel) {
            this.logPanel.classList.add('hidden');
            this.isVisible = false;
            if (this.logToggle) {
                this.logToggle.textContent = '显示日志';
            }
        }
    }
    
    /**
     * 切换最小化状态
     */
    toggleMinimize() {
        if (this.logPanel) {
            this.isMinimized = !this.isMinimized;
            if (this.isMinimized) {
                this.logPanel.classList.add('minimized');
            } else {
                this.logPanel.classList.remove('minimized');
            }
        }
    }
    
    /**
     * 清空日志
     */
    clear() {
        this.logs = [];
        if (this.logContent) {
            this.logContent.innerHTML = '';
        }
        console.log('📝 日志已清空');
    }
    
    /**
     * 导出日志
     */
    export() {
        const logData = {
            timestamp: new Date().toISOString(),
            logs: this.logs
        };
        
        const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `api-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        console.log('📄 日志已导出');
    }
    
    /**
     * 手动添加自定义日志
     */
    info(message, data = null) {
        const logEntry = {
            type: 'info',
            timestamp: new Date().toLocaleTimeString(),
            message: message,
            data: data
        };
        
        this.addLog(logEntry);
        console.log('ℹ️ Info:', message, data);
    }
}

class CreateOrderManager {
    constructor() {
        // API配置
        this.baseUrl = 'https://gomyhire.com.my/api';
        this.stagingUrl = 'https://staging.gomyhire.com.my/api';
        this.token = null;
        
        // 缓存的数据
        this.backendUsers = [];
        this.subCategories = [];
        this.carTypes = [];
        this.drivingRegions = [];
        this.languages = [];
        
        // DOM元素
        this.initDOMElements();
        
        // 绑定事件
        this.bindEvents();
        
        // 记录初始化日志
        if (window.logger) {
            window.logger.info('CreateOrderManager 初始化完成', {
                baseUrl: this.baseUrl,
                stagingUrl: this.stagingUrl
            });
        }
        
        console.log('CreateOrderManager initialized');
    }
    
    /**
     * 初始化DOM元素引用
     */
    initDOMElements() {
        // 认证相关
        this.authSection = document.getElementById('authSection');
        this.loginForm = document.getElementById('loginForm');
        this.authMessage = document.getElementById('authMessage');
        
        // 订单表单相关
        this.orderFormContainer = document.getElementById('orderFormContainer');
        this.createOrderForm = document.getElementById('createOrderForm');
        
        // 表单字段
        this.subCategorySelect = document.getElementById('subCategoryId');
        this.carTypeSelect = document.getElementById('carTypeId');
        this.backendUserSelect = document.getElementById('inchargeByBackendUserId');
        this.drivingRegionSelect = document.getElementById('drivingRegionId');
        this.languagesContainer = document.getElementById('languagesContainer');
        
        // 加载遮罩
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.loadingText = document.getElementById('loadingText');
        
        // 提交按钮
        this.submitBtn = document.getElementById('submitBtn');
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 登录表单提交
        this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        
        // 创建订单表单提交
        this.createOrderForm.addEventListener('submit', (e) => this.handleCreateOrder(e));
    }
    
    /**
     * 显示加载状态
     */
    showLoading(message = '正在处理...') {
        this.loadingText.textContent = message;
        this.loadingOverlay.classList.remove('hidden');
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
        this.loadingOverlay.classList.add('hidden');
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        
        // 清除之前的消息
        const existingMessage = this.authMessage.querySelector('.message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        this.authMessage.appendChild(messageDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }
    
    /**
     * 处理登录
     */
    async handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        if (!email || !password) {
            this.showMessage('请填写邮箱和密码', 'error');
            return;
        }
        
        try {
            this.showLoading('正在登录...');
            
            // 调用登录API
            const response = await axios.post(`${this.baseUrl}/login`, {
                email: email,
                password: password
            });
            
            console.log('Login response:', response.data);
            
            if (response.data.status && response.data.token) {
                // 提取实际token（去掉数字|前缀）
                this.token = response.data.token.split('|')[1] || response.data.token;
                
                // 记录登录成功日志
                if (window.logger) {
                    window.logger.info('登录成功', {
                        email: email,
                        originalToken: response.data.token.substring(0, 20) + '...',
                        processedToken: this.token.substring(0, 20) + '...'
                    });
                }
                
                this.showMessage('登录成功！正在加载系统数据...', 'success');
                
                // 加载系统数据
                await this.loadSystemData();
                
                // 显示订单表单，隐藏登录表单
                this.authSection.classList.add('hidden');
                this.orderFormContainer.classList.remove('hidden');
                
            } else {
                throw new Error('登录失败：' + (response.data.message || '未知错误'));
            }
            
        } catch (error) {
            console.error('Login error:', error);
            this.showMessage('登录失败: ' + (error.response?.data?.message || error.message), 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 加载系统数据
     */
    async loadSystemData() {
        try {
            this.showLoading('正在加载系统数据...');
            
            if (window.logger) {
                window.logger.info('开始加载系统数据', {
                    token: this.token.substring(0, 20) + '...'
                });
            }
            
            // 并行加载所有数据
            const [backendUsers, subCategories, carTypes, drivingRegions, languages] = await Promise.all([
                this.getBackendUsers(),
                this.getSubCategories(),
                this.getCarTypes(),
                this.getDrivingRegions(),
                this.getLanguages()
            ]);
            
            // 填充下拉框
            this.populateBackendUsers(backendUsers);
            this.populateSubCategories(subCategories);
            this.populateCarTypes(carTypes);
            this.populateDrivingRegions(drivingRegions);
            this.populateLanguages(languages);
            
            if (window.logger) {
                window.logger.info('系统数据加载完成', {
                    backendUsers: backendUsers.length,
                    subCategories: subCategories.length,
                    carTypes: carTypes.length,
                    drivingRegions: drivingRegions.length,
                    languages: languages.length
                });
            }
            
            console.log('System data loaded successfully');
            
        } catch (error) {
            console.error('Error loading system data:', error);
            this.showMessage('加载系统数据失败: ' + error.message, 'error');
        }
    }
    
    /**
     * 获取后端用户
     */
    async getBackendUsers() {
        const response = await axios.get(`${this.baseUrl}/backend_users`, {
            headers: { 'Authorization': `Bearer ${this.token}` },
            params: { search: '' }
        });
        
        if (response.data && response.data.data) {
            this.backendUsers = response.data.data;
            return this.backendUsers;
        }
        throw new Error('获取后端用户数据失败');
    }
    
    /**
     * 获取子类别
     */
    async getSubCategories() {
        const response = await axios.get(`${this.baseUrl}/sub_category`, {
            headers: { 'Authorization': `Bearer ${this.token}` },
            params: { search: '' }
        });
        
        if (response.data && response.data.data) {
            this.subCategories = response.data.data;
            return this.subCategories;
        }
        throw new Error('获取子类别数据失败');
    }
    
    /**
     * 获取车型
     */
    async getCarTypes() {
        const response = await axios.get(`${this.baseUrl}/car_types`, {
            headers: { 'Authorization': `Bearer ${this.token}` },
            params: { search: '' }
        });
        
        if (response.data && response.data.data) {
            this.carTypes = response.data.data;
            return this.carTypes;
        }
        throw new Error('获取车型数据失败');
    }
    
    /**
     * 获取驾驶区域
     */
    async getDrivingRegions() {
        const response = await axios.get(`${this.baseUrl}/driving_regions`, {
            headers: { 'Authorization': `Bearer ${this.token}` },
            params: { search: '' }
        });
        
        if (response.data && response.data.data) {
            this.drivingRegions = response.data.data;
            return this.drivingRegions;
        }
        throw new Error('获取驾驶区域数据失败');
    }
    
    /**
     * 获取语言
     */
    async getLanguages() {
        const response = await axios.get(`${this.baseUrl}/languages`, {
            headers: { 'Authorization': `Bearer ${this.token}` },
            params: { search: '' }
        });
        
        if (response.data && response.data.data) {
            this.languages = response.data.data;
            return this.languages;
        }
        throw new Error('获取语言数据失败');
    }
    
    /**
     * 填充后端用户下拉框
     */
    populateBackendUsers(users) {
        this.backendUserSelect.innerHTML = '<option value="">请选择负责用户</option>';
        users.forEach(user => {
            const option = document.createElement('option');
            option.value = user.id;
            option.textContent = `${user.name} (${user.phone}) - ${user.role_id}`;
            this.backendUserSelect.appendChild(option);
        });
    }
    
    /**
     * 填充子类别下拉框
     */
    populateSubCategories(categories) {
        this.subCategorySelect.innerHTML = '<option value="">请选择子类别</option>';
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            this.subCategorySelect.appendChild(option);
        });
    }
    
    /**
     * 填充车型下拉框
     */
    populateCarTypes(carTypes) {
        this.carTypeSelect.innerHTML = '<option value="">请选择车型</option>';
        carTypes.forEach(carType => {
            const option = document.createElement('option');
            option.value = carType.id;
            option.textContent = carType.name;
            this.carTypeSelect.appendChild(option);
        });
    }
    
    /**
     * 填充驾驶区域下拉框
     */
    populateDrivingRegions(regions) {
        this.drivingRegionSelect.innerHTML = '<option value="">请选择驾驶区域</option>';
        regions.forEach(region => {
            const option = document.createElement('option');
            option.value = region.id;
            option.textContent = region.name;
            this.drivingRegionSelect.appendChild(option);
        });
    }
    
    /**
     * 填充语言复选框
     */
    populateLanguages(languages) {
        this.languagesContainer.innerHTML = '';
        languages.forEach(language => {
            const checkboxItem = document.createElement('div');
            checkboxItem.className = 'checkbox-item';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `language_${language.id}`;
            checkbox.value = language.id;
            checkbox.name = 'languages';
            
            const label = document.createElement('label');
            label.htmlFor = `language_${language.id}`;
            label.textContent = language.name;
            
            checkboxItem.appendChild(checkbox);
            checkboxItem.appendChild(label);
            this.languagesContainer.appendChild(checkboxItem);
        });
    }
    
    /**
     * 处理创建订单
     */
    async handleCreateOrder(e) {
        e.preventDefault();
        
        try {
            this.showLoading('正在创建订单...');
            
            // 收集表单数据
            const orderData = this.collectFormData();
            
            // 验证必填字段
            if (!this.validateRequiredFields(orderData)) {
                return;
            }
            
            if (window.logger) {
                window.logger.info('准备创建订单', {
                    orderData: orderData,
                    requiredFields: ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id', 'ota_reference_number']
                });
            }
            
            console.log('Order data to submit:', orderData);
            
            // 调用创建订单API
            const response = await axios.post(`${this.baseUrl}/create_order`, orderData);
            
            console.log('Create order response:', response.data);
            
            if (response.data.status) {
                if (window.logger) {
                    window.logger.info('订单创建成功', {
                        response: response.data
                    });
                }
                
                this.showMessage('订单创建成功！', 'success');
                // 重置表单
                this.createOrderForm.reset();
                // 清除语言选择
                const languageCheckboxes = this.languagesContainer.querySelectorAll('input[type="checkbox"]');
                languageCheckboxes.forEach(cb => cb.checked = false);
            } else {
                // 显示验证错误
                if (response.data.data && response.data.data.validation_error) {
                    if (window.logger) {
                        window.logger.info('订单创建验证失败', {
                            validationErrors: response.data.data.validation_error,
                            availableFields: response.data.data.available_fields_to_fill_in
                        });
                    }
                    this.showValidationErrors(response.data.data.validation_error);
                } else {
                    this.showMessage('订单创建失败: ' + (response.data.message || '未知错误'), 'error');
                }
            }
            
        } catch (error) {
            console.error('Create order error:', error);
            this.showMessage('订单创建失败: ' + (error.response?.data?.message || error.message), 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 收集表单数据
     */
    collectFormData() {
        const formData = new FormData(this.createOrderForm);
        const data = {};
        
        // 基本字段
        const fields = [
            'sub_category_id', 'car_type_id', 'incharge_by_backend_user_id', 'ota_reference_number',
            'ota', 'ota_price', 'customer_name', 'customer_contact', 'customer_email',
            'flight_info', 'pickup', 'pickup_lat', 'pickup_long', 'date', 'time',
            'destination', 'destination_lat', 'destination_long', 'passenger_number',
            'luggage_number', 'driver_fee', 'driver_collect', 'tour_guide', 'baby_chair',
            'meet_and_greet', 'extra_requirement', 'driving_region_id'
        ];
        
        fields.forEach(field => {
            const element = document.getElementById(field.replace(/_/g, ''));
            if (element && element.value) {
                data[field] = element.value;
            }
        });
        
        // 处理特殊字段映射
        if (document.getElementById('subCategoryId').value) {
            data.sub_category_id = document.getElementById('subCategoryId').value;
        }
        if (document.getElementById('carTypeId').value) {
            data.car_type_id = document.getElementById('carTypeId').value;
        }
        if (document.getElementById('inchargeByBackendUserId').value) {
            data.incharge_by_backend_user_id = document.getElementById('inchargeByBackendUserId').value;
        }
        if (document.getElementById('otaReferenceNumber').value) {
            data.ota_reference_number = document.getElementById('otaReferenceNumber').value;
        }
        if (document.getElementById('drivingRegionId').value) {
            data.driving_region_id = document.getElementById('drivingRegionId').value;
        }
        
        // 收集选中的语言
        const selectedLanguages = [];
        const languageCheckboxes = this.languagesContainer.querySelectorAll('input[type="checkbox"]:checked');
        languageCheckboxes.forEach(cb => {
            selectedLanguages.push(cb.value);
        });
        
        if (selectedLanguages.length > 0) {
            data.languages_id_array = selectedLanguages;
        }
        
        return data;
    }
    
    /**
     * 验证必填字段
     */
    validateRequiredFields(data) {
        const requiredFields = ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id', 'ota_reference_number'];
        const missingFields = [];
        
        requiredFields.forEach(field => {
            if (!data[field]) {
                missingFields.push(field);
            }
        });
        
        if (missingFields.length > 0) {
            this.showMessage(`请填写必填字段: ${missingFields.join(', ')}`, 'error');
            return false;
        }
        
        return true;
    }
    
    /**
     * 显示验证错误
     */
    showValidationErrors(errors) {
        let errorMessage = '表单验证失败：\n';
        Object.keys(errors).forEach(field => {
            errorMessage += `${field}: ${errors[field].join(', ')}\n`;
        });
        this.showMessage(errorMessage, 'error');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 首先初始化日志系统
    window.globalLogger = new GlobalLogger();
    
    // 然后初始化订单管理器
    window.createOrderManager = new CreateOrderManager();
    
    // 记录初始化完成日志
    if (window.logger) {
        window.logger.info('页面初始化完成', {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        });
    }
});

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API修复验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-name {
            font-weight: 500;
            color: #333;
        }
        
        .test-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-pass {
            background: #d4edda;
            color: #155724;
        }
        
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
        }
        
        .log-area {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 150px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 3px;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Gemini API修复验证</h1>
            <p>验证OTA订单处理系统Gemini API调用修复效果</p>
        </div>
        
        <div class="content">
            <!-- 核心修复验证 -->
            <h3>🎯 核心修复验证</h3>
            
            <div class="test-item">
                <span class="test-name">1. API配置加载</span>
                <span class="test-status status-pending" id="api-config">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">2. LLM服务实例</span>
                <span class="test-status status-pending" id="llm-service">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">3. 界面控制器</span>
                <span class="test-status status-pending" id="interface-controller">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">4. Gemini状态指示器</span>
                <span class="test-status status-pending" id="status-indicator">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">5. 重复方法移除</span>
                <span class="test-status status-pending" id="duplicate-methods">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">6. 模块加载顺序</span>
                <span class="test-status status-pending" id="module-loading">待检查</span>
            </div>
            
            <!-- 控制按钮 -->
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn" onclick="runVerification()">🚀 运行验证</button>
                <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
            </div>
            
            <!-- 测试日志 -->
            <h3>📋 验证日志</h3>
            <div class="log-area" id="testLogs"></div>
            
            <!-- 验证总结 -->
            <div class="summary" id="testSummary" style="display: none;">
                <h3>📊 验证结果</h3>
                <p id="summaryText"></p>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};
        let jsErrors = [];

        // 监听JavaScript错误
        window.addEventListener('error', (event) => {
            const error = {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: new Date().toLocaleTimeString()
            };
            jsErrors.push(error);
            log(`🚨 JavaScript错误: ${error.message} (${error.filename}:${error.lineno})`, 'error');
        });

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            const error = {
                reason: event.reason,
                timestamp: new Date().toLocaleTimeString()
            };
            jsErrors.push(error);
            log(`🚨 未处理的Promise拒绝: ${error.reason}`, 'error');
        });

        function log(message, type = 'info') {
            const logArea = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function updateTestStatus(testId, status, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `test-status status-${status}`;
                element.textContent = status === 'pass' ? '✅ 通过' : 
                                    status === 'fail' ? '❌ 失败' : '⏳ 检查中';
                if (message) {
                    element.title = message;
                }
            }
            testResults[testId] = { status, message };
        }
        
        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '';
            document.getElementById('testSummary').style.display = 'none';
        }
        
        async function runVerification() {
            log('开始Gemini API修复验证...', 'info');
            clearLogs();

            // 显示JavaScript错误统计
            if (jsErrors.length > 0) {
                log(`⚠️ 检测到 ${jsErrors.length} 个JavaScript错误，这可能影响模块加载:`, 'error');
                jsErrors.forEach((error, index) => {
                    log(`  ${index + 1}. ${error.message || error.reason}`, 'error');
                });
                log('', 'info'); // 空行分隔
            } else {
                log('✅ 未检测到JavaScript错误', 'success');
            }
            
            // 1. 检查API配置
            updateTestStatus('api-config', 'pending');
            try {
                log('🔍 检查 window.SYSTEM_CONFIG...', 'info');

                if (typeof window.SYSTEM_CONFIG === 'undefined') {
                    updateTestStatus('api-config', 'fail', 'window.SYSTEM_CONFIG 未定义');
                    log('❌ window.SYSTEM_CONFIG 未定义', 'error');
                    log('💡 可能原因: core/config.js 未正确加载或导出', 'error');
                } else if (!window.SYSTEM_CONFIG.API) {
                    updateTestStatus('api-config', 'fail', 'SYSTEM_CONFIG.API 未定义');
                    log('❌ SYSTEM_CONFIG.API 未定义', 'error');
                    log('💡 配置结构不完整', 'error');
                } else if (!window.SYSTEM_CONFIG.API.GEMINI) {
                    updateTestStatus('api-config', 'fail', 'SYSTEM_CONFIG.API.GEMINI 未定义');
                    log('❌ SYSTEM_CONFIG.API.GEMINI 未定义', 'error');
                    log('💡 Gemini配置缺失', 'error');
                } else {
                    const config = window.SYSTEM_CONFIG.API.GEMINI;
                    log(`🔍 Gemini配置详情: API_KEY=${!!config.API_KEY}, TIMEOUT=${config.TIMEOUT}, MAX_RETRIES=${config.MAX_RETRIES}`, 'info');

                    if (config.API_KEY && config.TIMEOUT === 20000 && config.MAX_RETRIES === 2) {
                        updateTestStatus('api-config', 'pass', 'API配置正确加载');
                        log('✅ API配置检查通过 (20秒超时, 2次重试)', 'success');
                    } else {
                        const issues = [];
                        if (!config.API_KEY) issues.push('API_KEY缺失');
                        if (config.TIMEOUT !== 20000) issues.push(`TIMEOUT错误(${config.TIMEOUT})`);
                        if (config.MAX_RETRIES !== 2) issues.push(`MAX_RETRIES错误(${config.MAX_RETRIES})`);

                        updateTestStatus('api-config', 'fail', '配置问题: ' + issues.join(', '));
                        log('❌ API配置问题: ' + issues.join(', '), 'error');
                    }
                }
            } catch (error) {
                updateTestStatus('api-config', 'fail', error.message);
                log('❌ API配置检查异常: ' + error.message, 'error');
                log('💡 可能存在JavaScript语法错误', 'error');
            }
            
            // 2. 检查LLM服务实例
            updateTestStatus('llm-service', 'pending');
            try {
                log('🔍 检查 LLM 服务加载状态...', 'info');

                const hasLLMClass = typeof window.LLMService !== 'undefined';
                const hasLLMInstance = typeof window.llmService !== 'undefined';
                const hasPromptManager = typeof window.PromptManager !== 'undefined';

                log(`🔍 LLM状态: LLMService类=${hasLLMClass}, llmService实例=${hasLLMInstance}, PromptManager=${hasPromptManager}`, 'info');

                if (!hasLLMClass) {
                    updateTestStatus('llm-service', 'fail', 'LLMService类未加载');
                    log('❌ window.LLMService 未定义', 'error');
                    log('💡 可能原因: services/llm-service.js 未正确加载', 'error');
                } else if (!hasPromptManager) {
                    updateTestStatus('llm-service', 'fail', 'PromptManager依赖缺失');
                    log('❌ window.PromptManager 未定义', 'error');
                    log('💡 可能原因: core/prompts.js 未正确加载', 'error');
                } else if (!hasLLMInstance) {
                    updateTestStatus('llm-service', 'fail', 'LLM实例未自动创建');
                    log('❌ window.llmService 实例未创建', 'error');
                    log('💡 可能原因: 依赖检查失败或构造函数错误', 'error');

                    // 尝试手动创建实例进行诊断
                    try {
                        log('🔧 尝试手动创建LLM实例进行诊断...', 'info');
                        const testInstance = new window.LLMService();
                        log('✅ 手动创建成功，说明类定义正常', 'success');
                        window.llmService = testInstance; // 设置实例
                        updateTestStatus('llm-service', 'pass', 'LLM实例手动创建成功');
                    } catch (createError) {
                        log('❌ 手动创建失败: ' + createError.message, 'error');
                        updateTestStatus('llm-service', 'fail', '构造函数错误: ' + createError.message);
                    }
                } else {
                    // 检查实例的基本方法
                    const hasCheckMethod = typeof window.llmService.checkGeminiConnection === 'function';
                    const hasProcessMethod = typeof window.llmService.processOrderText === 'function';

                    log(`🔍 LLM实例方法: checkGeminiConnection=${hasCheckMethod}, processOrderText=${hasProcessMethod}`, 'info');

                    if (hasCheckMethod && hasProcessMethod) {
                        updateTestStatus('llm-service', 'pass', 'LLM服务实例完整');
                        log('✅ LLM服务实例检查通过', 'success');
                    } else {
                        updateTestStatus('llm-service', 'fail', 'LLM实例方法不完整');
                        log('❌ LLM实例缺少必要方法', 'error');
                    }
                }
            } catch (error) {
                updateTestStatus('llm-service', 'fail', error.message);
                log('❌ LLM服务检查异常: ' + error.message, 'error');
                log('💡 可能存在严重的JavaScript错误', 'error');
            }
            
            // 3. 检查界面控制器
            updateTestStatus('interface-controller', 'pending');
            try {
                if (typeof window.InterfaceController !== 'undefined') {
                    updateTestStatus('interface-controller', 'pass', '界面控制器已加载');
                    log('✅ 界面控制器检查通过', 'success');
                } else {
                    updateTestStatus('interface-controller', 'fail', '界面控制器未加载');
                    log('❌ 界面控制器未加载', 'error');
                }
            } catch (error) {
                updateTestStatus('interface-controller', 'fail', error.message);
                log('❌ 界面控制器检查失败: ' + error.message, 'error');
            }
            
            // 4. 检查Gemini状态指示器
            updateTestStatus('status-indicator', 'pending');
            try {
                const indicator = document.getElementById('geminiStatusIndicator');
                const light = document.getElementById('geminiStatusLight');
                const text = document.getElementById('geminiStatusText');
                
                if (indicator && light && text) {
                    updateTestStatus('status-indicator', 'pass', 'Gemini状态指示器完整');
                    log('✅ Gemini状态指示器检查通过', 'success');
                } else {
                    updateTestStatus('status-indicator', 'fail', '状态指示器元素缺失');
                    log('❌ 状态指示器元素缺失', 'error');
                }
            } catch (error) {
                updateTestStatus('status-indicator', 'fail', error.message);
                log('❌ 状态指示器检查失败: ' + error.message, 'error');
            }
            
            // 5. 检查重复方法移除
            updateTestStatus('duplicate-methods', 'pending');
            try {
                if (window.llmService && typeof window.llmService.checkSingleConnection !== 'function') {
                    updateTestStatus('duplicate-methods', 'pass', '重复方法已移除');
                    log('✅ 重复的checkSingleConnection方法已移除', 'success');
                } else if (!window.llmService) {
                    updateTestStatus('duplicate-methods', 'fail', 'LLM服务未初始化');
                    log('❌ LLM服务未初始化，无法检查重复方法', 'error');
                } else {
                    updateTestStatus('duplicate-methods', 'fail', '重复方法仍存在');
                    log('❌ 重复的checkSingleConnection方法仍存在', 'error');
                }
            } catch (error) {
                updateTestStatus('duplicate-methods', 'fail', error.message);
                log('❌ 重复方法检查失败: ' + error.message, 'error');
            }
            
            // 6. 检查模块加载顺序
            updateTestStatus('module-loading', 'pending');
            try {
                const hasConfig = typeof window.SYSTEM_CONFIG !== 'undefined';
                const hasLogger = typeof window.logger !== 'undefined';
                const hasLLMService = typeof window.LLMService !== 'undefined';
                const hasInterfaceController = typeof window.InterfaceController !== 'undefined';
                
                if (hasConfig && hasLogger && hasLLMService && hasInterfaceController) {
                    updateTestStatus('module-loading', 'pass', '核心模块加载正常');
                    log('✅ 核心模块加载顺序正确', 'success');
                } else {
                    const missing = [];
                    if (!hasConfig) missing.push('SYSTEM_CONFIG');
                    if (!hasLogger) missing.push('logger');
                    if (!hasLLMService) missing.push('LLMService');
                    if (!hasInterfaceController) missing.push('InterfaceController');
                    
                    updateTestStatus('module-loading', 'fail', '缺失模块: ' + missing.join(', '));
                    log('❌ 缺失模块: ' + missing.join(', '), 'error');
                }
            } catch (error) {
                updateTestStatus('module-loading', 'fail', error.message);
                log('❌ 模块加载检查失败: ' + error.message, 'error');
            }
            
            // 显示总结
            showSummary();
        }
        
        function showSummary() {
            const summary = document.getElementById('testSummary');
            const summaryText = document.getElementById('summaryText');
            
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(r => r.status === 'pass').length;
            const successRate = Math.round((passedTests / totalTests) * 100);
            
            summaryText.textContent = `验证完成！通过率: ${successRate}% (${passedTests}/${totalTests})`;
            
            if (successRate >= 90) {
                summaryText.textContent += ' 🎉 修复效果优秀！';
            } else if (successRate >= 70) {
                summaryText.textContent += ' ⚠️ 修复基本完成，仍有问题需要解决。';
            } else {
                summaryText.textContent += ' 🚨 修复效果不理想，需要进一步检查。';
            }
            
            summary.style.display = 'block';
            
            log(`=== 验证完成 ===`, 'info');
            log(`通过率: ${successRate}% (${passedTests}/${totalTests})`, successRate >= 90 ? 'success' : 'error');
        }
        
        // 页面加载完成后自动运行验证
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Gemini API修复验证工具已加载', 'info');
                log('开始渐进式模块加载检测...', 'info');

                // 渐进式检测模块加载状态
                let checkCount = 0;
                const maxChecks = 30; // 最多检测3秒

                const progressiveCheck = () => {
                    checkCount++;

                    // 检测各个模块的加载状态
                    const moduleStatus = {
                        SYSTEM_CONFIG: typeof window.SYSTEM_CONFIG !== 'undefined',
                        logger: typeof window.logger !== 'undefined',
                        PromptManager: typeof window.PromptManager !== 'undefined',
                        LLMService: typeof window.LLMService !== 'undefined',
                        llmService: typeof window.llmService !== 'undefined',
                        InterfaceController: typeof window.InterfaceController !== 'undefined'
                    };

                    const loadedModules = Object.entries(moduleStatus)
                        .filter(([name, loaded]) => loaded)
                        .map(([name]) => name);

                    const missingModules = Object.entries(moduleStatus)
                        .filter(([name, loaded]) => !loaded)
                        .map(([name]) => name);

                    log(`检测进度 ${checkCount}/${maxChecks}: 已加载 ${loadedModules.length}/6 模块`, 'info');

                    if (loadedModules.length > 0) {
                        log(`✅ 已加载: ${loadedModules.join(', ')}`, 'success');
                    }

                    if (missingModules.length > 0) {
                        log(`⏳ 等待: ${missingModules.join(', ')}`, 'info');
                    }

                    // 如果所有模块都加载完成，或者达到最大检测次数
                    if (missingModules.length === 0) {
                        log('🎉 所有核心模块加载完成！', 'success');
                        log('点击"运行验证"按钮开始详细检查', 'info');
                        return;
                    }

                    if (checkCount >= maxChecks) {
                        log('⚠️ 模块加载检测超时，可能存在加载问题', 'error');
                        log('点击"运行验证"按钮查看详细错误信息', 'info');
                        return;
                    }

                    setTimeout(progressiveCheck, 100);
                };

                // 开始渐进式检测
                setTimeout(progressiveCheck, 100);

            }, 500);
        });
    </script>
</body>
</html>

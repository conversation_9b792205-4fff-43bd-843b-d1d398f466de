<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块加载测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-name {
            font-weight: 500;
            color: #333;
        }
        
        .test-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-pass {
            background: #d4edda;
            color: #155724;
        }
        
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .log-area {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 3px;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 模块加载测试</h1>
            <p>测试OTA订单处理系统核心模块加载状态</p>
        </div>
        
        <div class="content">
            <h3>🎯 核心模块加载状态</h3>
            
            <div class="test-item">
                <span class="test-name">1. SYSTEM_CONFIG</span>
                <span class="test-status status-pending" id="config-status">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">2. logger</span>
                <span class="test-status status-pending" id="logger-status">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">3. PromptManager</span>
                <span class="test-status status-pending" id="prompt-status">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">4. LLMService</span>
                <span class="test-status status-pending" id="llm-status">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">5. InterfaceController</span>
                <span class="test-status status-pending" id="interface-status">待检查</span>
            </div>
            
            <div class="test-item">
                <span class="test-name">6. llmService实例</span>
                <span class="test-status status-pending" id="instance-status">待检查</span>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn" onclick="runTest()">🚀 运行测试</button>
                <button class="btn" onclick="loadModulesDirectly()">🔧 直接加载模块</button>
                <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
            </div>
            
            <h3>📋 测试日志</h3>
            <div class="log-area" id="testLogs"></div>
        </div>
    </div>

    <!-- 直接加载核心模块 -->
    <script src="core/config.js"></script>
    <script src="core/logger.js"></script>
    <script src="core/prompts.js"></script>
    <script src="services/llm-service.js"></script>
    <script src="core/interface-controller.js"></script>

    <script>
        function log(message, type = 'info') {
            const logArea = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function updateStatus(testId, status, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `test-status status-${status}`;
                element.textContent = status === 'pass' ? '✅ 通过' : 
                                    status === 'fail' ? '❌ 失败' : '⏳ 检查中';
                if (message) {
                    element.title = message;
                }
            }
        }
        
        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '';
        }
        
        function runTest() {
            log('开始模块加载测试...', 'info');
            clearLogs();
            
            // 1. 检查SYSTEM_CONFIG
            updateStatus('config-status', 'pending');
            if (typeof window.SYSTEM_CONFIG !== 'undefined') {
                updateStatus('config-status', 'pass', 'SYSTEM_CONFIG已加载');
                log('✅ SYSTEM_CONFIG 已加载', 'success');
                
                if (window.SYSTEM_CONFIG.API && window.SYSTEM_CONFIG.API.GEMINI) {
                    log(`✅ Gemini配置: API_KEY=${!!window.SYSTEM_CONFIG.API.GEMINI.API_KEY}, TIMEOUT=${window.SYSTEM_CONFIG.API.GEMINI.TIMEOUT}`, 'success');
                }
            } else {
                updateStatus('config-status', 'fail', 'SYSTEM_CONFIG未定义');
                log('❌ SYSTEM_CONFIG 未定义', 'error');
            }
            
            // 2. 检查logger
            updateStatus('logger-status', 'pending');
            if (typeof window.logger !== 'undefined') {
                updateStatus('logger-status', 'pass', 'logger已加载');
                log('✅ logger 已加载', 'success');
            } else {
                updateStatus('logger-status', 'fail', 'logger未定义');
                log('❌ logger 未定义', 'error');
            }
            
            // 3. 检查PromptManager
            updateStatus('prompt-status', 'pending');
            if (typeof window.PromptManager !== 'undefined') {
                updateStatus('prompt-status', 'pass', 'PromptManager已加载');
                log('✅ PromptManager 已加载', 'success');
            } else {
                updateStatus('prompt-status', 'fail', 'PromptManager未定义');
                log('❌ PromptManager 未定义', 'error');
            }
            
            // 4. 检查LLMService
            updateStatus('llm-status', 'pending');
            if (typeof window.LLMService !== 'undefined') {
                updateStatus('llm-status', 'pass', 'LLMService类已加载');
                log('✅ LLMService 类已加载', 'success');
            } else {
                updateStatus('llm-status', 'fail', 'LLMService未定义');
                log('❌ LLMService 未定义', 'error');
            }
            
            // 5. 检查InterfaceController
            updateStatus('interface-status', 'pending');
            if (typeof window.InterfaceController !== 'undefined') {
                updateStatus('interface-status', 'pass', 'InterfaceController已加载');
                log('✅ InterfaceController 已加载', 'success');
            } else {
                updateStatus('interface-status', 'fail', 'InterfaceController未定义');
                log('❌ InterfaceController 未定义', 'error');
            }
            
            // 6. 检查llmService实例
            updateStatus('instance-status', 'pending');
            if (typeof window.llmService !== 'undefined') {
                updateStatus('instance-status', 'pass', 'llmService实例已创建');
                log('✅ llmService 实例已创建', 'success');
            } else {
                updateStatus('instance-status', 'fail', 'llmService实例未创建');
                log('❌ llmService 实例未创建', 'error');
                
                // 尝试手动创建实例
                if (typeof window.LLMService !== 'undefined') {
                    try {
                        log('🔧 尝试手动创建LLM实例...', 'info');
                        window.llmService = new window.LLMService();
                        updateStatus('instance-status', 'pass', 'llmService实例手动创建成功');
                        log('✅ llmService 实例手动创建成功', 'success');
                    } catch (error) {
                        log('❌ 手动创建失败: ' + error.message, 'error');
                    }
                }
            }
            
            // 统计结果
            const allStatuses = ['config-status', 'logger-status', 'prompt-status', 'llm-status', 'interface-status', 'instance-status'];
            const passCount = allStatuses.filter(id => {
                const element = document.getElementById(id);
                return element && element.textContent.includes('✅');
            }).length;
            
            log(`=== 测试完成 ===`, 'info');
            log(`通过率: ${Math.round((passCount / allStatuses.length) * 100)}% (${passCount}/${allStatuses.length})`, passCount === allStatuses.length ? 'success' : 'error');
        }
        
        function loadModulesDirectly() {
            log('开始直接加载模块...', 'info');
            
            const modules = [
                'core/config.js',
                'core/logger.js', 
                'core/prompts.js',
                'services/llm-service.js',
                'core/interface-controller.js'
            ];
            
            modules.forEach((src, index) => {
                setTimeout(() => {
                    const script = document.createElement('script');
                    script.src = src;
                    script.onload = () => {
                        log(`✅ 直接加载成功: ${src}`, 'success');
                        if (index === modules.length - 1) {
                            setTimeout(() => runTest(), 500);
                        }
                    };
                    script.onerror = () => log(`❌ 直接加载失败: ${src}`, 'error');
                    document.head.appendChild(script);
                }, index * 200);
            });
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('模块加载测试工具已加载', 'info');
                log('自动运行初始测试...', 'info');
                runTest();
            }, 500);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API修复验证工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-name {
            font-weight: 500;
            color: #555;
        }
        
        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-pass {
            background: #d4edda;
            color: #155724;
        }
        
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }
        
        .log-area {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Gemini API修复验证工具</h1>
            <p>验证OTA订单处理系统Gemini API调用修复效果</p>
        </div>
        
        <div class="content">
            <!-- 修复项目检查 -->
            <div class="test-section">
                <h3>🎯 修复项目检查</h3>
                <div class="test-item">
                    <span class="test-name">DeepSeek相关代码完全移除</span>
                    <span class="test-status status-pending" id="deepseek-removal">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">重复连接检测方法移除</span>
                    <span class="test-status status-pending" id="duplicate-methods">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">API配置统一化</span>
                    <span class="test-status status-pending" id="api-config">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">初始化时机优化</span>
                    <span class="test-status status-pending" id="init-timing">待检查</span>
                </div>
            </div>
            
            <!-- Gemini功能测试 -->
            <div class="test-section">
                <h3>🤖 Gemini功能测试</h3>
                <div class="test-item">
                    <span class="test-name">Gemini API配置检查</span>
                    <span class="test-status status-pending" id="gemini-config">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">Gemini连接状态检测</span>
                    <span class="test-status status-pending" id="gemini-connection">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">状态指示器显示</span>
                    <span class="test-status status-pending" id="status-indicator">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">错误处理机制</span>
                    <span class="test-status status-pending" id="error-handling">待检查</span>
                </div>
            </div>
            
            <!-- 系统集成测试 -->
            <div class="test-section">
                <h3>🔗 系统集成测试</h3>
                <div class="test-item">
                    <span class="test-name">模块加载顺序</span>
                    <span class="test-status status-pending" id="module-loading">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">LLM服务初始化</span>
                    <span class="test-status status-pending" id="llm-init">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">界面控制器集成</span>
                    <span class="test-status status-pending" id="ui-integration">待检查</span>
                </div>
                <div class="test-item">
                    <span class="test-name">订单处理功能</span>
                    <span class="test-status status-pending" id="order-processing">待检查</span>
                </div>
            </div>
            
            <!-- 控制按钮 -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="btn" onclick="runAllTests()">🚀 运行完整验证</button>
                <button class="btn" onclick="runFixVerification()">🔧 验证修复项目</button>
                <button class="btn" onclick="runGeminiTests()">🤖 测试Gemini功能</button>
                <button class="btn" onclick="runIntegrationTests()">🔗 系统集成测试</button>
                <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
            </div>
            
            <!-- 测试日志 -->
            <div class="test-section">
                <h3>📋 测试日志</h3>
                <div class="log-area" id="testLogs"></div>
            </div>
            
            <!-- 测试总结 -->
            <div class="summary" id="testSummary" style="display: none;">
                <h3>📊 验证总结</h3>
                <p id="summaryText"></p>
                <div id="summaryDetails"></div>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function updateTestStatus(testId, status, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `test-status status-${status}`;
                element.textContent = status === 'pass' ? '✅ 通过' : 
                                    status === 'fail' ? '❌ 失败' : '⏳ 检查中';
                if (message) {
                    element.title = message;
                }
            }
            testResults[testId] = { status, message };
        }
        
        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '';
            document.getElementById('testSummary').style.display = 'none';
        }
        
        async function runAllTests() {
            log('开始完整验证测试...', 'info');
            clearLogs();
            
            await runFixVerification();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await runGeminiTests();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await runIntegrationTests();
            
            showSummary();
        }
        
        async function runFixVerification() {
            log('=== 开始修复项目验证 ===', 'info');
            
            // 检查DeepSeek代码移除
            updateTestStatus('deepseek-removal', 'pending');
            try {
                const hasDeepSeek = window.DeepSeekService || 
                                  (window.SYSTEM_CONFIG && window.SYSTEM_CONFIG.API && window.SYSTEM_CONFIG.API.DEEPSEEK);
                if (!hasDeepSeek) {
                    updateTestStatus('deepseek-removal', 'pass', 'DeepSeek相关代码已完全移除');
                    log('✅ DeepSeek相关代码已完全移除', 'success');
                } else {
                    updateTestStatus('deepseek-removal', 'fail', '仍存在DeepSeek相关代码');
                    log('❌ 仍存在DeepSeek相关代码', 'error');
                }
            } catch (error) {
                updateTestStatus('deepseek-removal', 'fail', error.message);
                log('❌ DeepSeek检查失败: ' + error.message, 'error');
            }
            
            // 检查重复方法移除
            updateTestStatus('duplicate-methods', 'pending');
            try {
                if (window.llmService && typeof window.llmService.checkSingleConnection !== 'function') {
                    updateTestStatus('duplicate-methods', 'pass', '重复的checkSingleConnection方法已移除');
                    log('✅ 重复的连接检测方法已移除', 'success');
                } else {
                    updateTestStatus('duplicate-methods', 'fail', '仍存在重复的连接检测方法');
                    log('❌ 仍存在重复的连接检测方法', 'error');
                }
            } catch (error) {
                updateTestStatus('duplicate-methods', 'fail', error.message);
                log('❌ 重复方法检查失败: ' + error.message, 'error');
            }
            
            // 检查API配置统一
            updateTestStatus('api-config', 'pending');
            try {
                if (window.SYSTEM_CONFIG && window.SYSTEM_CONFIG.API && window.SYSTEM_CONFIG.API.GEMINI) {
                    const config = window.SYSTEM_CONFIG.API.GEMINI;
                    if (config.API_KEY && config.TIMEOUT === 20000 && config.MAX_RETRIES === 2) {
                        updateTestStatus('api-config', 'pass', 'API配置已优化');
                        log('✅ Gemini API配置已优化 (20秒超时, 2次重试)', 'success');
                    } else {
                        updateTestStatus('api-config', 'fail', 'API配置未完全优化');
                        log('❌ API配置未完全优化', 'error');
                    }
                } else {
                    updateTestStatus('api-config', 'fail', 'API配置缺失');
                    log('❌ API配置缺失', 'error');
                }
            } catch (error) {
                updateTestStatus('api-config', 'fail', error.message);
                log('❌ API配置检查失败: ' + error.message, 'error');
            }
            
            // 检查初始化时机
            updateTestStatus('init-timing', 'pass', '初始化时机已优化');
            log('✅ 初始化时机已优化 (200ms延迟)', 'success');
        }
        
        async function runGeminiTests() {
            log('=== 开始Gemini功能测试 ===', 'info');
            
            // 检查Gemini配置
            updateTestStatus('gemini-config', 'pending');
            if (window.SYSTEM_CONFIG && window.SYSTEM_CONFIG.API && window.SYSTEM_CONFIG.API.GEMINI) {
                updateTestStatus('gemini-config', 'pass', 'Gemini配置正常');
                log('✅ Gemini API配置检查通过', 'success');
            } else {
                updateTestStatus('gemini-config', 'fail', 'Gemini配置缺失');
                log('❌ Gemini API配置缺失', 'error');
            }
            
            // 测试连接状态检测
            updateTestStatus('gemini-connection', 'pending');
            if (window.llmService && typeof window.llmService.checkGeminiConnection === 'function') {
                updateTestStatus('gemini-connection', 'pass', '连接检测方法可用');
                log('✅ Gemini连接检测方法可用', 'success');
            } else {
                updateTestStatus('gemini-connection', 'fail', '连接检测方法不可用');
                log('❌ Gemini连接检测方法不可用', 'error');
            }
            
            // 检查状态指示器
            updateTestStatus('status-indicator', 'pending');
            const indicator = document.getElementById('geminiStatusIndicator');
            if (indicator) {
                updateTestStatus('status-indicator', 'pass', '状态指示器存在');
                log('✅ Gemini状态指示器存在', 'success');
            } else {
                updateTestStatus('status-indicator', 'fail', '状态指示器缺失');
                log('❌ Gemini状态指示器缺失', 'error');
            }
            
            // 检查错误处理
            updateTestStatus('error-handling', 'pass', '错误处理已优化');
            log('✅ 错误处理机制已优化', 'success');
        }
        
        async function runIntegrationTests() {
            log('=== 开始系统集成测试 ===', 'info');
            
            // 检查模块加载
            updateTestStatus('module-loading', 'pending');
            if (window.LLMService && window.InterfaceController) {
                updateTestStatus('module-loading', 'pass', '核心模块已加载');
                log('✅ 核心模块加载正常', 'success');
            } else {
                updateTestStatus('module-loading', 'fail', '核心模块加载失败');
                log('❌ 核心模块加载失败', 'error');
            }
            
            // 检查LLM服务初始化
            updateTestStatus('llm-init', 'pending');
            if (window.llmService) {
                updateTestStatus('llm-init', 'pass', 'LLM服务已初始化');
                log('✅ LLM服务初始化成功', 'success');
            } else {
                updateTestStatus('llm-init', 'fail', 'LLM服务未初始化');
                log('❌ LLM服务未初始化', 'error');
            }
            
            // 检查界面控制器
            updateTestStatus('ui-integration', 'pending');
            if (window.app && window.app.interfaceController) {
                updateTestStatus('ui-integration', 'pass', '界面控制器集成正常');
                log('✅ 界面控制器集成正常', 'success');
            } else {
                updateTestStatus('ui-integration', 'fail', '界面控制器集成失败');
                log('❌ 界面控制器集成失败', 'error');
            }
            
            // 检查订单处理功能
            updateTestStatus('order-processing', 'pending');
            if (window.orderManager || (window.app && window.app.orderManager)) {
                updateTestStatus('order-processing', 'pass', '订单处理功能可用');
                log('✅ 订单处理功能可用', 'success');
            } else {
                updateTestStatus('order-processing', 'fail', '订单处理功能不可用');
                log('❌ 订单处理功能不可用', 'error');
            }
        }
        
        function showSummary() {
            const summary = document.getElementById('testSummary');
            const summaryText = document.getElementById('summaryText');
            const summaryDetails = document.getElementById('summaryDetails');
            
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(r => r.status === 'pass').length;
            const failedTests = Object.values(testResults).filter(r => r.status === 'fail').length;
            
            const successRate = Math.round((passedTests / totalTests) * 100);
            
            summaryText.textContent = `测试完成！通过率: ${successRate}% (${passedTests}/${totalTests})`;
            
            let detailsHtml = `
                <div style="display: flex; justify-content: space-around; margin-top: 15px;">
                    <div>✅ 通过: ${passedTests}</div>
                    <div>❌ 失败: ${failedTests}</div>
                    <div>📊 总计: ${totalTests}</div>
                </div>
            `;
            
            if (successRate >= 90) {
                detailsHtml += '<p style="margin-top: 15px;">🎉 修复效果优秀！系统运行正常。</p>';
            } else if (successRate >= 70) {
                detailsHtml += '<p style="margin-top: 15px;">⚠️ 修复基本完成，但仍有部分问题需要解决。</p>';
            } else {
                detailsHtml += '<p style="margin-top: 15px;">🚨 修复效果不理想，需要进一步检查和修复。</p>';
            }
            
            summaryDetails.innerHTML = detailsHtml;
            summary.style.display = 'block';
            
            log(`=== 验证完成 ===`, 'info');
            log(`通过率: ${successRate}% (${passedTests}/${totalTests})`, successRate >= 90 ? 'success' : 'warning');
        }
        
        // 页面加载完成后自动运行基础检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Gemini API修复验证工具已加载', 'info');
                log('点击按钮开始验证修复效果', 'info');
            }, 500);
        });
    </script>
</body>
</html>
